import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { SafeAreaView, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { homeScreenStyles } from './styles';

export default function HomeScreen() {
  const { user, signOut } = useAuth();

  // Mock data - in real app, this would come from database
  const [savingsData, setSavingsData] = useState({
    currentAmount: 2500000,
    targetAmount: 10000000,
    selectedAgent: 'Ibu Sari',
    agentPhone: '0812-3456-7890'
  });

  const progressPercentage = Math.round((savingsData.currentAmount / savingsData.targetAmount) * 100);

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(auth)/login');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <SafeAreaView style={homeScreenStyles.container}>
      <ScrollView contentContainerStyle={homeScreenStyles.scrollContent}>
        {/* Header */}
        <View style={homeScreenStyles.header}>
          <Text style={homeScreenStyles.appTitle}>SaveMoney</Text>
          <TouchableOpacity style={homeScreenStyles.signOutButton} onPress={handleSignOut}>
            <Ionicons name="log-out-outline" size={24} color="#fff" />
            <Text style={homeScreenStyles.signOutText}>Keluar</Text>
          </TouchableOpacity>
        </View>

        {/* Welcome Message */}
        <View style={homeScreenStyles.welcomeSection}>
          <Text style={homeScreenStyles.welcomeText}>Selamat Datang!</Text>
          <Text style={homeScreenStyles.userEmail}>{user?.email}</Text>
        </View>

        {/* Progress Tabungan */}
        <View style={homeScreenStyles.progressSection}>
          <Text style={homeScreenStyles.sectionTitle}>Progress Tabungan</Text>

          <View style={homeScreenStyles.progressContainer}>
            <View style={homeScreenStyles.progressBar}>
              <View
                style={[
                  homeScreenStyles.progressFill,
                  { width: `${Math.min(progressPercentage, 100)}%` }
                ]}
              />
            </View>
            <Text style={homeScreenStyles.progressText}>{progressPercentage}%</Text>
          </View>

          <View style={homeScreenStyles.amountInfo}>
            <Text style={homeScreenStyles.currentAmountLabel}>Tabungan Saat Ini:</Text>
            <Text style={homeScreenStyles.currentAmount}>{formatCurrency(savingsData.currentAmount)}</Text>
            <Text style={homeScreenStyles.targetAmountLabel}>Target: {formatCurrency(savingsData.targetAmount)}</Text>
          </View>
        </View>

        {/* Agent Information */}
        <View style={homeScreenStyles.agentSection}>
          <Text style={homeScreenStyles.sectionTitle}>Agent Anda</Text>
          <View style={homeScreenStyles.agentCard}>
            <View style={homeScreenStyles.agentIcon}>
              <Ionicons name="person" size={32} color="#4CAF50" />
            </View>
            <View style={homeScreenStyles.agentInfo}>
              <Text style={homeScreenStyles.agentName}>{savingsData.selectedAgent}</Text>
              <Text style={homeScreenStyles.agentPhone}>{savingsData.agentPhone}</Text>
            </View>
            <TouchableOpacity style={homeScreenStyles.callButton}>
              <Ionicons name="call" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}